<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="text-3xl font-bold">Subscription Plans</h2>
            @if($activeSubscription)
                <div class="flex items-center">
                    <span class="mr-2">Subscription Status:</span>
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                        Active
                    </span>
                </div>
            @else
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
                    No Active Subscription
                </span>
            @endif
        </div>
    </x-slot>

    <div class="container mx-auto px-4 py-8" x-data="{}">
        <div class="flex justify-between items-center mb-6">
            @if($activeSubscription)
                <div class="bg-blue-100 border-l-4 border-blue-500 text-blue-700 p-4 flex-grow" role="alert">
                    <p class="font-bold">Current Plan: {{ $currentPlan->name }}</p>
                    <p>Expires: {{ $activeSubscription->ends_at->format('M d, Y') }}</p>
                    <p class="mt-2">Status:
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            Active
                        </span>
                    </p>
                </div>
            @endif
            <div class="ml-4">
                <x-splade-link href="{{ route('patient.subscriptions.history') }}" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    View History
                </x-splade-link>
            </div>
        </div>

        <!-- Plan Comparison Table -->
        <div class="mb-10 overflow-x-auto">
            <h3 class="text-xl font-semibold mb-4">Plan Comparison</h3>
            <table class="min-w-full bg-white border border-gray-200 rounded-lg overflow-hidden">
                <thead class="bg-gray-100">
                    <tr>
                        <th class="py-3 px-4 text-left font-semibold border-b">Feature</th>
                        @foreach($plans as $plan)
                            <th class="py-3 px-4 text-center font-semibold border-b {{ $activeSubscription && $activeSubscription->plan_id === $plan->id ? 'bg-blue-100' : ($plan->is_featured ? 'bg-yellow-50' : '') }}">
                                {{ $plan->name }}
                                @if($plan->is_featured)
                                    <span class="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800">Recommended</span>
                                @endif
                            </th>
                        @endforeach
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td class="py-3 px-4 border-b font-medium">Price</td>
                        @foreach($plans as $plan)
                            <td class="py-3 px-4 text-center border-b {{ $activeSubscription && $activeSubscription->plan_id === $plan->id ? 'bg-blue-50' : ($plan->is_featured ? 'bg-yellow-50' : '') }}">
                                ${{ number_format($plan->price, 2) }}
                            </td>
                        @endforeach
                    </tr>
                    <tr>
                        <td class="py-3 px-4 border-b font-medium">Duration</td>
                        @foreach($plans as $plan)
                            <td class="py-3 px-4 text-center border-b {{ $activeSubscription && $activeSubscription->plan_id === $plan->id ? 'bg-blue-50' : ($plan->is_featured ? 'bg-yellow-50' : '') }}">
                                {{ $plan->duration_months }} month(s)
                            </td>
                        @endforeach
                    </tr>
                    <tr>
                        <td class="py-3 px-4 border-b font-medium">Service Limit</td>
                        @foreach($plans as $plan)
                            <td class="py-3 px-4 text-center border-b {{ $activeSubscription && $activeSubscription->plan_id === $plan->id ? 'bg-blue-50' : ($plan->is_featured ? 'bg-yellow-50' : '') }}">
                                {{ $plan->service_limit ? $plan->service_limit : 'Unlimited' }}
                            </td>
                        @endforeach
                    </tr>
                    <tr>
                        <td class="py-3 px-4 border-b font-medium">Medication Orders</td>
                        @foreach($plans as $plan)
                            <td class="py-3 px-4 text-center border-b {{ $activeSubscription && $activeSubscription->plan_id === $plan->id ? 'bg-blue-50' : ($plan->is_featured ? 'bg-yellow-50' : '') }}">
                                <span class="{{ $plan->service_limit ? '' : 'text-green-600' }}">
                                    {{ $plan->service_limit ? 'Limited' : 'Unlimited' }}
                                </span>
                            </td>
                        @endforeach
                    </tr>
                    <tr>
                        <td class="py-3 px-4 border-b font-medium">Consultations</td>
                        @foreach($plans as $plan)
                            <td class="py-3 px-4 text-center border-b {{ $activeSubscription && $activeSubscription->plan_id === $plan->id ? 'bg-blue-50' : ($plan->is_featured ? 'bg-yellow-50' : '') }}">
                                <span class="{{ $plan->service_limit ? '' : 'text-green-600' }}">
                                    {{ $plan->service_limit ? 'Limited' : 'Unlimited' }}
                                </span>
                            </td>
                        @endforeach
                    </tr>
                    <tr>
                        <td class="py-3 px-4 font-medium">Action</td>
                        @foreach($plans as $plan)
                            <td class="py-3 px-4 text-center {{ $activeSubscription && $activeSubscription->plan_id === $plan->id ? 'bg-blue-50' : ($plan->is_featured ? 'bg-yellow-50' : '') }}">
                                <button
                                    @click="$refs.creditCardManagement.showActivatePlanModal({{ json_encode($plan) }})"
                                    class="px-4 py-1 text-sm {{ $activeSubscription && $activeSubscription->plan_id === $plan->id ? 'bg-green-500 hover:bg-green-600' : 'bg-blue-500 hover:bg-blue-600' }} text-white rounded"
                                >
                                    @if($activeSubscription && $activeSubscription->plan_id === $plan->id)
                                        Renew
                                    @elseif($activeSubscription)
                                        Change
                                    @else
                                        Select
                                    @endif
                                </button>
                            </td>
                        @endforeach
                    </tr>
                </tbody>
            </table>
        </div>

        @if($showAllPlans)
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                @foreach($plans as $plan)
                <div class="bg-white rounded-lg shadow-lg p-6 {{ $activeSubscription && $activeSubscription->plan_id === $plan->id ? 'border-2 border-blue-500 relative' : ($plan->is_featured ? 'border-2 border-yellow-500 relative' : '') }}">
                    @if($activeSubscription && $activeSubscription->plan_id === $plan->id)
                        <div class="absolute top-0 right-0 bg-blue-500 text-white px-3 py-1 rounded-bl-lg rounded-tr-lg font-semibold text-sm">
                            Current Plan
                        </div>
                    @elseif($plan->is_featured)
                        <div class="absolute top-0 right-0 bg-yellow-500 text-white px-3 py-1 rounded-bl-lg rounded-tr-lg font-semibold text-sm">
                            Recommended
                        </div>
                    @endif
                    <h3 class="text-xl font-semibold mb-4">{{ $plan->name }}</h3>
                    <p class="text-3xl font-bold mb-4">${{ number_format($plan->price, 2) }}</p>
                    <ul class="mb-6">
                        <li>Duration: {{ $plan->duration_months }} month(s)</li>
                        <li>Services: {{ $plan->service_limit ? $plan->service_limit : 'Unlimited' }}</li>
                    </ul>
                    <button
                        @click="$refs.creditCardManagement.showActivatePlanModal({{ json_encode($plan) }})"
                        class="w-full {{ $activeSubscription && $activeSubscription->plan_id === $plan->id ? 'bg-green-500 hover:bg-green-600' : 'bg-blue-500 hover:bg-blue-600' }} text-white py-2 px-4 rounded"
                    >
                        @if($activeSubscription && $activeSubscription->plan_id === $plan->id)
                            Renew Plan
                        @elseif($activeSubscription)
                            Change to This Plan
                        @else
                            Subscribe Now
                        @endif
                    </button>
                </div>
                @endforeach
            </div>
        @else
            <p class="text-lg mb-4">You are currently on the Unlimited Plan. No other plans are available.</p>
        @endif

        <!-- Credit Card Management Component -->
        <div class="mt-10">
            <CreditCardManagement
                ref="creditCardManagement"
                :user-id="@js($user->id)"
                role="{{ $role }}"
            />
        </div>

        <x-splade-data default="{ referrals: [] }">
            <div class="bg-base-200 p-6 rounded-lg">
                <h1 class="text-xl font-bold mb-4">Test</h1>

                <CreditCardManagement
                    ref="creditCardManagement"
                    :user-id="@js($user->id)"
                    role="{{ $role }}"
                />
            </div>
        </x-splade-data>

        {{-- <x-splade-script>
            document.addEventListener('DOMContentLoaded', function() {
                window.addEventListener('splade:request-success', function(event) {
                    if (event.detail.url.includes('/p/credit-cards')) {
                        // Refresh the page to ensure the component is properly loaded
                        window.location.reload();
                    }
                });
            });
        </x-splade-script> --}}
    </div>
</x-app-layout>
