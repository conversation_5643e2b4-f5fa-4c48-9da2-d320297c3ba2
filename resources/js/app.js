import "./bootstrap";
import "../css/app.css";
import "../css/choices.scss";
import "@protonemedia/laravel-splade/dist/jodit.css";
import "../css/flatpickr.styl";
// import "./service-worker-registration";

import { createApp } from "vue/dist/vue.esm-bundler.js";
import { renderSpladeApp, SpladePlugin } from "@protonemedia/laravel-splade";
import AddToCartButton from './components/AddToCartButton.vue';
import BusinessTotalPlanPriceCalculator from './components/BusinessTotalPlanPriceCalculator.vue';
import CreditCardManagement from "./components/CreditCardManagement.vue";
import PaymentMethodManagement from "./components/PaymentMethodManagement.vue";
import CountdownTimer from './components/CountdownTimer.vue';
import DynamicMedicalQuestionnaire from './components/DynamicMedicalQuestionnaire.vue';
import ExitIntent from "./components/ExitIntent.vue";
import FilteredMedicationList from "./components/FilteredMedicationList.vue";
import ReferralForm from "./components/ReferralForm.vue";
import GenericsAZ from "./components/Medication/GenericsAZ.vue";
import GeneralMedicalQuestionForm from './components/GeneralMedicalQuestionForm.vue';
import InventoryValueTrendChart from './components/InventoryValueTrendChart.vue';
import LandingPageMedicationSelector from "./components/LandingPageMedicationSelector.vue";
import LandingPageForm from './components/LandingPageForm.vue';
import LandingPage4Form from './components/LandingPage4Form.vue';
import MedicationDetails from './components/Medication/Details.vue';
import MedicationFilter from './components/Medication/Filter/Index.vue';
import MedicationList from './components/Medication/List.vue';
import MedicationSelectPlan from './components/Medication/SelectPlan.vue';
import MedicationForm from './components/MedicationForm.vue';
import MedicationsWithUsesForm from "./components/MedicationsWithUsesForm.vue";
import OrderProgressBar from './components/OrderProgressBar.vue';
import PainTreatmentForm from './components/PainTreatmentForm.vue';
import PainTreatmentFormLandingPage4 from './components/PainTreatmentFormLandingPage4.vue';
import PrimaryUses from "./components/Medication/PrimaryUses.vue";
import QuestionManager from './components/QuestionManager.vue';
import VideoRecording from './components/VideoRecording.vue';
import SubscriptionPayment from './components/SubscriptionPayment.vue';
import ClipboardCopy from './components/ClipboardCopy.vue';
import CopyCredentials from './components/CopyCredentials.vue';

// Business Module Components
import BusinessDashboard from './components/business/BusinessDashboard.vue';
import EmployeeManagement from './components/business/EmployeeManagement.vue';

// Admin Module Components
import AgentManagement from './components/AgentManagement.vue';

// Agent Module Components
// Training
import QuizSection from './components/agent/training/QuizSection.vue';
import TrainingList from './components/agent/training/TrainingList.vue';
import TrainingCard from './components/agent/training/TrainingCard.vue';
import TrainingDetail from './components/agent/training/TrainingDetail.vue';
import CertificationList from './components/agent/training/CertificationList.vue';
import CertificationCard from './components/agent/training/CertificationCard.vue';

// Marketing
import MarketingMaterialsList from './components/agent/marketing/MarketingMaterialsList.vue';
import MarketingMaterialCard from './components/agent/marketing/MarketingMaterialCard.vue';
import ShareMaterialModal from './components/agent/marketing/ShareMaterialModal.vue';
import CustomizeMaterialModal from './components/agent/marketing/CustomizeMaterialModal.vue';

// Goals
import GoalsList from './components/agent/goals/GoalsList.vue';
import GoalCard from './components/agent/goals/GoalCard.vue';
import CreateGoalModal from './components/agent/goals/CreateGoalModal.vue';
import ViewGoalModal from './components/agent/goals/ViewGoalModal.vue';

// Reports
import ReportsList from './components/agent/reports/ReportsList.vue';
import ReportCard from './components/agent/reports/ReportCard.vue';
import ReportFormModal from './components/agent/reports/ReportFormModal.vue';
import ReportViewModal from './components/agent/reports/ReportViewModal.vue';

// Common
import NotificationsDropdown from './components/common/NotificationsDropdown.vue';

// Icons
import HeartIcon from './components/Icons/Heart.vue';
import ScissorsIcon from './components/Icons/Scissors.vue';
import RunningIcon from './components/Icons/Running.vue';
import BrainIcon from './components/Icons/Brain.vue';
import SparkleIcon from './components/Icons/Sparkle.vue';
import BandaidIcon from './components/Icons/Bandaid.vue';
import HealthShieldIcon from "./components/Icons/HealthShield.vue";

const el = document.getElementById("app");

createApp({
    render: renderSpladeApp({ el })
})
    .use(SpladePlugin, {
        "max_keep_alive": 10,
        "transform_anchors": false,
        "progress_bar": {
            // delay: 250,
            color: "#da395d",
            css: true,
            spinner: true,
        },
        "view_transitions": true,
        "components": {
            AddToCartButton,
            BusinessTotalPlanPriceCalculator,
            CreditCardManagement,
            CountdownTimer,
            PaymentMethodManagement,
            DynamicMedicalQuestionnaire,
            ExitIntent,
            FilteredMedicationList,
            ReferralForm,
            GeneralMedicalQuestionForm,
            GenericsAZ,
            InventoryValueTrendChart,
            LandingPageMedicationSelector,
            LandingPageForm,
            LandingPage4Form,
            MedicationDetails,
            MedicationFilter,
            MedicationForm,
            MedicationList,
            MedicationSelectPlan,
            MedicationsWithUsesForm,
            OrderProgressBar,
            PainTreatmentForm,
            PainTreatmentFormLandingPage4,
            PrimaryUses,
            QuestionManager,
            SubscriptionPayment,
            VideoRecording,
            ClipboardCopy,
            CopyCredentials,

            // Business Module Components
            BusinessDashboard,
            EmployeeManagement,

            // Admin Module Components
            AgentManagement,

            // Agent Module Components
            // Training
            CertificationCard,
            CertificationList,
            QuizSection,
            TrainingCard,
            TrainingDetail,
            TrainingList,

            // Marketing
            CustomizeMaterialModal,
            MarketingMaterialCard,
            MarketingMaterialsList,
            ShareMaterialModal,

            // Goals
            CreateGoalModal,
            GoalCard,
            GoalsList,
            ViewGoalModal,

            // Reports
            ReportCard,
            ReportFormModal,
            ReportsList,
            ReportViewModal,

            // Common
            NotificationsDropdown,

            // Icons
            BandaidIcon,
            HeartIcon,
            ScissorsIcon,
            RunningIcon,
            BrainIcon,
            SparkleIcon,
            HealthShieldIcon,
        }
    })
    .mount(el);
