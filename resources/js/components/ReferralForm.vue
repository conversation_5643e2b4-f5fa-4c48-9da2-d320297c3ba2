<template>
    <div class="referral-form">
        <x-splade-form
            :action="submitUrl"
            method="POST"
            class="space-y-4"
            @success="onSuccess"
            @error="onError"
        >
            <!-- Referral Type Selection -->
            <div class="form-control">
                <label class="label">
                    <span class="label-text font-medium">Referral Type <span class="text-error">*</span></span>
                </label>
                <x-splade-select
                    name="referral_type"
                    :options="referralTypes"
                    placeholder="Select referral type"
                    class="select select-bordered w-full"
                    v-model="form.referral_type"
                    @change="onReferralTypeChange"
                />
            </div>

            <!-- Referral Information -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="form-control">
                    <label class="label">
                        <span class="label-text font-medium">
                            {{ getReferralNameLabel() }} <span class="text-error">*</span>
                        </span>
                    </label>
                    <x-splade-input
                        name="referral_name"
                        type="text"
                        :placeholder="getReferralNamePlaceholder()"
                        class="input input-bordered w-full"
                        v-model="form.referral_name"
                    />
                </div>

                <div class="form-control">
                    <label class="label">
                        <span class="label-text font-medium">Email Address <span class="text-error">*</span></span>
                    </label>
                    <x-splade-input
                        name="referral_email"
                        type="email"
                        placeholder="Enter email address"
                        class="input input-bordered w-full"
                        v-model="form.referral_email"
                    />
                </div>
            </div>

            <!-- Phone Number -->
            <div class="form-control">
                <label class="label">
                    <span class="label-text font-medium">Phone Number</span>
                </label>
                <x-splade-input
                    name="referral_phone"
                    type="tel"
                    placeholder="Enter phone number"
                    class="input input-bordered w-full"
                    v-model="form.referral_phone"
                />
            </div>

            <!-- Target Agent (for agent referrals) -->
            <div v-if="form.referral_type === 'agent'" class="form-control">
                <label class="label">
                    <span class="label-text font-medium">Target Agent <span class="text-error">*</span></span>
                </label>
                <x-splade-select
                    name="target_agent_id"
                    :options="availableAgents"
                    placeholder="Select target agent"
                    class="select select-bordered w-full"
                    v-model="form.target_agent_id"
                />
                <label class="label">
                    <span class="label-text-alt">Select the agent who will handle this referral</span>
                </label>
            </div>

            <!-- Notes -->
            <div class="form-control">
                <label class="label">
                    <span class="label-text font-medium">Notes</span>
                </label>
                <x-splade-textarea
                    name="notes"
                    placeholder="Add any additional notes about this referral..."
                    class="textarea textarea-bordered w-full h-24"
                    v-model="form.notes"
                />
                <label class="label">
                    <span class="label-text-alt">Maximum 1000 characters</span>
                </label>
            </div>

            <!-- Options -->
            <div class="form-control">
                <label class="label cursor-pointer justify-start gap-3">
                    <x-splade-checkbox
                        name="send_notification"
                        class="checkbox checkbox-primary"
                        v-model="form.send_notification"
                        :checked="true"
                    />
                    <span class="label-text">Send email notification to referral</span>
                </label>
            </div>

            <!-- Follow-up Date -->
            <div class="form-control">
                <label class="label">
                    <span class="label-text font-medium">Follow-up Date</span>
                </label>
                <x-splade-input
                    name="follow_up_date"
                    type="date"
                    class="input input-bordered w-full"
                    v-model="form.follow_up_date"
                    :min="tomorrow"
                />
                <label class="label">
                    <span class="label-text-alt">Optional: Set a reminder to follow up</span>
                </label>
            </div>

            <!-- Submit Button -->
            <div class="form-control mt-6">
                <button
                    type="submit"
                    class="btn btn-primary w-full"
                    :class="{ 'loading': form.processing }"
                    :disabled="form.processing || !isFormValid"
                >
                    <svg v-if="!form.processing" class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    {{ form.processing ? 'Creating Referral...' : 'Create Referral' }}
                </button>
            </div>
        </x-splade-form>
    </div>
</template>

<script setup>
import { computed, ref, reactive } from 'vue'

// Props
const props = defineProps({
    availableAgents: {
        type: Object,
        default: () => ({})
    },
    submitUrl: {
        type: String,
        required: true
    }
})

// Form data
const form = reactive({
    referral_type: '',
    referral_name: '',
    referral_email: '',
    referral_phone: '',
    target_agent_id: '',
    notes: '',
    send_notification: true,
    follow_up_date: ''
})

// Referral types
const referralTypes = {
    'patient': 'Patient Referral',
    'business': 'Business Referral',
    'agent': 'Agent Referral'
}

// Computed properties
const tomorrow = computed(() => {
    const date = new Date()
    date.setDate(date.getDate() + 1)
    return date.toISOString().split('T')[0]
})

const isFormValid = computed(() => {
    const hasRequiredFields = form.referral_type && form.referral_name && form.referral_email

    if (form.referral_type === 'agent') {
        return hasRequiredFields && form.target_agent_id
    }

    return hasRequiredFields
})

// Methods
const getReferralNameLabel = () => {
    switch (form.referral_type) {
        case 'patient':
            return 'Patient Name'
        case 'business':
            return 'Company Name'
        case 'agent':
            return 'Agent Name'
        default:
            return 'Name'
    }
}

const getReferralNamePlaceholder = () => {
    switch (form.referral_type) {
        case 'patient':
            return 'Enter patient full name'
        case 'business':
            return 'Enter company name'
        case 'agent':
            return 'Enter agent full name'
        default:
            return 'Enter name'
    }
}

const onReferralTypeChange = () => {
    // Clear target agent when changing referral type
    if (form.referral_type !== 'agent') {
        form.target_agent_id = ''
    }
}

const onSuccess = (response) => {
    // Reset form
    form.reset()

    // Show success message (handled by Toast in controller)
    console.log('Referral created successfully:', response)
}

const onError = (errors) => {
    console.error('Form validation errors:', errors)
}
</script>
