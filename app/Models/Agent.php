<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Str;

class Agent extends Model
{
    // Agent tiers with their commission rates
    const TIERS = [
        'SFMO' => 55, // Super Field Marketing Organization (55%)
        'FMO' => 50,  // Field Marketing Organization (50%)
        'SVG' => 45,  // Super Vendor Group (45%)
        'MGA' => 40,  // Managing General Agent (40%)
        'AGENT' => 30, // Regular Agent (30%)
        'ASSOCIATE' => 20 // Associate Agent (20%)
    ];

    // Tier hierarchy (higher index = higher tier)
    const TIER_HIERARCHY = [
        'ASSOCIATE' => 0,
        'AGENT' => 1,
        'MGA' => 2,
        'SVG' => 3,
        'FMO' => 4,
        'SFMO' => 5
    ];

    protected $fillable = [
        'user_id',
        'referring_agent_id',
        'company',
        'experience',
        'status',
        'tier',
        'commission_rate',
        'referral_code',
        'referral_token',
        'npn'
    ];

    /**
     * Get the user that owns the agent profile.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the agent who referred this agent.
     */
    public function referrer(): BelongsTo
    {
        return $this->belongsTo(Agent::class, 'referring_agent_id');
    }

    /**
     * Get the agents referred by this agent.
     */
    public function referrals(): HasMany
    {
        return $this->hasMany(Agent::class, 'referring_agent_id');
    }

    /**
     * Get the commissions earned by this agent.
     */
    public function commissions(): HasMany
    {
        return $this->hasMany(AgentCommission::class);
    }

    /**
     * Get the upline commissions earned by this agent.
     */
    public function uplineCommissions(): HasMany
    {
        return $this->hasMany(AgentCommission::class, 'upline_agent_id');
    }

    /**
     * Get the patients directly referred by this agent.
     */
    public function referredPatients()
    {
        return $this->hasMany(User::class, 'referring_agent_id');
    }

    /**
     * Get the businesses directly referred by this agent.
     */
    public function referredBusinesses()
    {
        return $this->hasMany(Business::class, 'referring_agent_id');
    }

    /**
     * Generate a unique referral code and token for this agent.
     */
    public function generateReferralCode(): void
    {
        $this->referral_code = strtoupper(Str::random(8));
        $this->referral_token = Str::uuid();
        $this->save();
    }



    /**
     * Get the allowed tiers for new agents referred by this agent.
     */
    public function getAllowedTiersForReferrals(): array
    {
        $myTierLevel = self::TIER_HIERARCHY[$this->tier] ?? 0;

        // Filter tiers that are lower than the current agent's tier
        return array_filter(self::TIERS, function($tier, $tierName) use ($myTierLevel) {
            return self::TIER_HIERARCHY[$tierName] < $myTierLevel;
        }, ARRAY_FILTER_USE_BOTH);
    }

    /**
     * Generate tier-specific referral URLs for this agent.
     *
     * @return array Array of tier-specific referral URLs
     */
    public function generateTierSpecificReferralUrls(): array
    {
        // Ensure agent has a referral code
        if (!$this->referral_code) {
            $this->generateReferralCode();
        }

        $allowedTiers = $this->getAllowedTiersForReferrals();
        $referralUrls = [];

        foreach ($allowedTiers as $tierName => $rate) {
            $referralUrls[$tierName] = [
                'url' => url('/agent/register') . '?agent_ref=' . $this->referral_code . '&tier=' . $tierName,
                'rate' => $rate
            ];
        }

        return $referralUrls;
    }

    /**
     * Generate patient referral URLs for this agent.
     *
     * @return array Array of patient referral URLs
     */
    public function generatePatientReferralUrls(): array
    {
        // Ensure agent has a referral code
        if (!$this->referral_code) {
            $this->generateReferralCode();
        }

        return [
            'rx' => [
                'name' => 'Prescription Medication',
                'url' => url('/rx') . '?agent_ref=' . $this->referral_code,
            ],
            'urgent_care' => [
                'name' => 'Urgent Care',
                'url' => url('/urgent-care') . '?agent_ref=' . $this->referral_code,
            ],
            'health_plan' => [
                'name' => 'Health Plan',
                'url' => url('/health-plan') . '?agent_ref=' . $this->referral_code,
            ],
            'home' => [
                'name' => 'General Registration',
                'url' => url('/') . '?agent_ref=' . $this->referral_code,
            ],
        ];
    }

    /**
     * Generate business referral URL for this agent.
     *
     * @return string Business referral URL
     */
    public function generateBusinessReferralUrl(): string
    {
        // Ensure agent has a referral code
        if (!$this->referral_code) {
            $this->generateReferralCode();
        }

        return url('/business/register') . '?agent_ref=' . $this->referral_code;
    }

    /**
     * Check if this agent can refer agents of a specific tier.
     */
    public function canReferTier(string $tier): bool
    {
        $myTierLevel = self::TIER_HIERARCHY[$this->tier] ?? 0;
        $targetTierLevel = self::TIER_HIERARCHY[$tier] ?? 0;

        // Can only refer lower tiers
        return $targetTierLevel < $myTierLevel;
    }

    /**
     * Calculate commission for a given transaction amount.
     */
    public function calculateCommission(float $amount): float
    {
        // Check if agent is eligible for commission (excludes LOA)
        if (!$this->user || !$this->user->isEligibleForCommission()) {
            return 0.0;
        }

        return round($amount * ($this->commission_rate / 100), 2);
    }

    /**
     * Calculate upline commission difference for a given transaction.
     */
    public function calculateUplineCommission(float $amount, Agent $downlineAgent): float
    {
        // Check if this agent is eligible for upline commissions (excludes LOA)
        if (!$this->user || !$this->user->isEligibleForCommission()) {
            return 0.0;
        }

        // If no referrer or referrer's rate is lower, no upline commission
        if (!$this->referrer || $this->commission_rate <= $downlineAgent->commission_rate) {
            return 0;
        }

        // Calculate the difference between rates
        $rateDifference = $this->commission_rate - $downlineAgent->commission_rate;

        return round($amount * ($rateDifference / 100), 2);
    }

    /**
     * Get the training progress for this agent.
     */
    public function trainingProgress(): HasMany
    {
        return $this->hasMany(AgentTrainingProgress::class);
    }

    /**
     * Get the earned certifications for this agent.
     */
    public function earnedCertifications(): HasMany
    {
        return $this->hasMany(AgentEarnedCertification::class);
    }

    /**
     * Get the marketing usage records for this agent.
     */
    public function marketingUsage(): HasMany
    {
        return $this->hasMany(AgentMarketingUsage::class);
    }

    /**
     * Get the landing pages for this agent.
     */
    public function landingPages(): HasMany
    {
        return $this->hasMany(AgentLandingPage::class);
    }

    /**
     * Get the messages sent by this agent.
     */
    public function sentMessages(): HasMany
    {
        return $this->hasMany(AgentMessage::class, 'sender_id', 'user_id');
    }

    /**
     * Get the messages received by this agent.
     */
    public function receivedMessages(): HasMany
    {
        return $this->hasMany(AgentMessage::class, 'recipient_id', 'user_id');
    }

    /**
     * Get the announcement reads for this agent.
     */
    public function announcementReads(): HasMany
    {
        return $this->hasMany(AgentAnnouncementRead::class);
    }

    /**
     * Get the support tickets for this agent.
     */
    public function supportTickets(): HasMany
    {
        return $this->hasMany(AgentSupportTicket::class);
    }

    /**
     * Get the goals for this agent.
     */
    public function goals(): HasMany
    {
        return $this->hasMany(AgentGoal::class);
    }

    /**
     * Get the custom reports for this agent.
     */
    public function customReports(): HasMany
    {
        return $this->hasMany(AgentCustomReport::class);
    }

    /**
     * Get the report exports for this agent.
     */
    public function reportExports(): HasMany
    {
        return $this->hasMany(AgentReportExport::class);
    }

    /**
     * Get the notification settings for this agent.
     */
    public function notificationSettings(): HasOne
    {
        return $this->hasOne(NotificationSetting::class);
    }

    /**
     * Get the subscriptions associated with this agent.
     */
    public function subscriptions(): HasMany
    {
        return $this->hasMany(Subscription::class);
    }
}
