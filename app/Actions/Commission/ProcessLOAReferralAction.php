<?php

namespace App\Actions\Commission;

use App\Actions\Action;
use App\Models\User;
use App\Models\Agent;
use App\Models\LOAReferral;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use App\Mail\LOAReferralNotification;

class ProcessLOAReferralAction extends Action
{
    /**
     * Process a referral created by an LOA user.
     *
     * @param array $data
     * @return array
     */
    public function execute(array $data): array
    {
        try {
            DB::beginTransaction();

            // Validate LOA user
            $loaUser = User::find($data['loa_user_id']);
            if (!$loaUser || !$loaUser->isLOA()) {
                return [
                    'success' => false,
                    'message' => 'Invalid LOA user'
                ];
            }

            // Validate target agent (if specified)
            $targetAgent = null;
            if (isset($data['target_agent_id'])) {
                $targetAgent = Agent::find($data['target_agent_id']);
                if (!$targetAgent || $targetAgent->status !== 'approved') {
                    return [
                        'success' => false,
                        'message' => 'Invalid target agent'
                    ];
                }
            }

            // Create LOA referral record
            $referral = $this->createLOAReferral($data, $loaUser, $targetAgent);

            // Process the referral based on type
            $result = $this->processReferralByType($referral, $data);

            if (!$result['success']) {
                DB::rollBack();
                return $result;
            }

            // Send notifications
            $this->sendReferralNotifications($referral, $data);

            DB::commit();

            Log::info('LOA referral processed successfully', [
                'loa_user_id' => $loaUser->id,
                'referral_id' => $referral->id,
                'referral_type' => $data['referral_type']
            ]);

            return [
                'success' => true,
                'message' => 'LOA referral processed successfully',
                'referral' => $referral,
                'tracking_code' => $referral->tracking_code
            ];

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Failed to process LOA referral', [
                'error' => $e->getMessage(),
                'data' => $data,
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'Failed to process LOA referral: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Create LOA referral record.
     *
     * @param array $data
     * @param User $loaUser
     * @param Agent|null $targetAgent
     * @return LOAReferral
     */
    private function createLOAReferral(array $data, User $loaUser, ?Agent $targetAgent): LOAReferral
    {
        // Create the LOAReferral model if it doesn't exist
        if (!class_exists(LOAReferral::class)) {
            // For now, we'll use a generic approach
            // In a real implementation, you'd create the LOAReferral model
            $referralData = [
                'loa_user_id' => $loaUser->id,
                'target_agent_id' => $targetAgent?->id,
                'referral_type' => $data['referral_type'],
                'referral_email' => $data['referral_email'],
                'referral_name' => $data['referral_name'] ?? null,
                'referral_phone' => $data['referral_phone'] ?? null,
                'notes' => $data['notes'] ?? null,
                'status' => 'pending',
                'tracking_code' => $this->generateTrackingCode(),
                'created_at' => now(),
                'updated_at' => now()
            ];

            // Store in database (you'd need to create the loa_referrals table)
            $referralId = DB::table('loa_referrals')->insertGetId($referralData);

            // Return a mock object for now
            return (object) array_merge($referralData, ['id' => $referralId]);
        }

        return LOAReferral::create([
            'loa_user_id' => $loaUser->id,
            'target_agent_id' => $targetAgent?->id,
            'referral_type' => $data['referral_type'],
            'referral_email' => $data['referral_email'],
            'referral_name' => $data['referral_name'] ?? null,
            'referral_phone' => $data['referral_phone'] ?? null,
            'notes' => $data['notes'] ?? null,
            'status' => 'pending',
            'tracking_code' => $this->generateTrackingCode()
        ]);
    }

    /**
     * Process referral based on its type.
     *
     * @param mixed $referral
     * @param array $data
     * @return array
     */
    private function processReferralByType($referral, array $data): array
    {
        switch ($data['referral_type']) {
            case 'patient':
                return $this->processPatientReferral($referral, $data);
            case 'business':
                return $this->processBusinessReferral($referral, $data);
            case 'agent':
                return $this->processAgentReferral($referral, $data);
            default:
                return [
                    'success' => false,
                    'message' => 'Invalid referral type'
                ];
        }
    }

    /**
     * Process patient referral.
     *
     * @param mixed $referral
     * @param array $data
     * @return array
     */
    private function processPatientReferral($referral, array $data): array
    {
        // Create referral link for patient signup
        $referralUrl = url('/') . '?loa_ref=' . $referral->tracking_code;

        // If target agent is specified, also add agent referral tracking
        if (!empty($referral->target_agent_id)) {
            $targetAgent = Agent::find($referral->target_agent_id);
            if ($targetAgent && $targetAgent->referral_code) {
                $referralUrl .= '&agent_ref=' . $targetAgent->referral_code;
            }
        }

        // Store referral URL
        if (method_exists($referral, 'update')) {
            $referral->update(['referral_url' => $referralUrl]);
        } else {
            DB::table('loa_referrals')
                ->where('id', $referral->id)
                ->update(['referral_url' => $referralUrl]);
        }

        return [
            'success' => true,
            'referral_url' => $referralUrl,
            'target_agent_id' => $referral->target_agent_id ?? null
        ];
    }

    /**
     * Process business referral.
     *
     * @param mixed $referral
     * @param array $data
     * @return array
     */
    private function processBusinessReferral($referral, array $data): array
    {
        // Create referral link for business signup
        $referralUrl = url('/business/register') . '?loa_ref=' . $referral->tracking_code;

        // Store referral URL
        if (method_exists($referral, 'update')) {
            $referral->update(['referral_url' => $referralUrl]);
        } else {
            DB::table('loa_referrals')
                ->where('id', $referral->id)
                ->update(['referral_url' => $referralUrl]);
        }

        return [
            'success' => true,
            'referral_url' => $referralUrl
        ];
    }

    /**
     * Process agent referral.
     *
     * @param mixed $referral
     * @param array $data
     * @return array
     */
    private function processAgentReferral($referral, array $data): array
    {
        // Create referral link for agent signup
        $referralUrl = url('/agent/register') . '?loa_ref=' . $referral->tracking_code;

        // Store referral URL
        if (method_exists($referral, 'update')) {
            $referral->update(['referral_url' => $referralUrl]);
        } else {
            DB::table('loa_referrals')
                ->where('id', $referral->id)
                ->update(['referral_url' => $referralUrl]);
        }

        return [
            'success' => true,
            'referral_url' => $referralUrl
        ];
    }

    /**
     * Send referral notifications.
     *
     * @param mixed $referral
     * @param array $data
     * @return void
     */
    private function sendReferralNotifications($referral, array $data): void
    {
        try {
            // Send email to the referred person if email is provided
            if (!empty($data['referral_email']) && class_exists(LOAReferralNotification::class)) {
                Mail::to($data['referral_email'])->send(new LOAReferralNotification($referral));
            }

            // Notify target agent if specified
            if (!empty($referral->target_agent_id)) {
                $targetAgent = Agent::find($referral->target_agent_id);
                if ($targetAgent && $targetAgent->user) {
                    // Send notification to target agent
                    $targetAgent->user->notify(new \App\Notifications\NewLOAReferralNotification($referral));
                }
            }

        } catch (\Exception $e) {
            Log::warning('Failed to send LOA referral notifications', [
                'referral_id' => $referral->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Generate unique tracking code for referral.
     *
     * @return string
     */
    private function generateTrackingCode(): string
    {
        return 'LOA-' . strtoupper(substr(md5(uniqid(rand(), true)), 0, 8));
    }
}
