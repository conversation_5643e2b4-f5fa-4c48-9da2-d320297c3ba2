<?php

use App\Http\Controllers\Admin\QuestionManagerController;
use App\Http\Controllers\LandingPage\Medications\MainPageController as LandingPageMainMedication;
use App\Http\Controllers\MedicalRecordController;
use App\Http\Controllers\MedicationsController;
use App\Http\Controllers\ShareASaleController;
use App\Http\Controllers\UrgentCare\ConditionsController as UrgentCareConditions;
use App\Http\Controllers\Users\DoctorsSignupController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

// No longer needed - direct download is handled in the main controller

Route::middleware('splade')->group(function () {
    // Registers routes to support the interactive components...
    Route::spladeWithVueBridge();

    // Registers routes to support password confirmation in Form and Link components...
    Route::spladePasswordConfirmation();

    // Registers routes to support Table Bulk Actions and Exports...
    Route::spladeTable();

    // Registers routes to support async File Uploads with Filepond...
    Route::spladeUploads();

    // Route::get('/', [UrgentCareConditions::class, 'index'])->name('home');
    // Route::get('/', [LandingPageMainMedication::class, 'index'])->name('home');
    Route::get('/', [App\Http\Controllers\LandingPage\HealthPlan\MainPageController::class, 'index'])->name('home');

    Route::middleware(['auth', 'redirect.role'])->get('/dashboard', function () {
        return view('dashboard');
    })->name('dashboard');

    Route::middleware('auth')->group(function () {
        // Route::get('/dashboard', function () {
        //     return view('dashboard');
        // })->middleware(['verified'])->name('dashboard');

        // Profile Completion Routes (status only - dismissal removed for forced completion)
        Route::prefix('profile-completion')->name('profile-completion.')->group(function () {
            Route::get('/status', [\App\Http\Controllers\ProfileCompletionController::class, 'status'])->name('status');
        });

        // Medical Questionnaire Completion Routes
        Route::prefix('medical-questionnaire')->name('medical-questionnaire.')->group(function () {
            Route::post('/dismiss', [\App\Http\Controllers\MedicalQuestionnaireCompletionController::class, 'dismiss'])->name('dismiss');
            Route::get('/status', [\App\Http\Controllers\MedicalQuestionnaireCompletionController::class, 'status'])->name('status');
        });

        // Test Mail Routes
        Route::get('/test-mail', [\App\Http\Controllers\TestMailController::class, 'showForm'])->name('test-mail');
        Route::post('/test-mail', [\App\Http\Controllers\TestMailController::class, 'sendTestMail'])->name('test-mail.send');

        // Invoice Routes
        Route::prefix('invoices')->name('invoices.')->group(function () {
            Route::get('/', [\App\Http\Controllers\InvoiceController::class, 'index'])->name('index');
            Route::get('/{transaction}', [\App\Http\Controllers\InvoiceController::class, 'show'])->name('show');
            Route::get('/{transaction}/download', [\App\Http\Controllers\InvoiceController::class, 'download'])->name('download');
            Route::get('/{transaction}/regenerate', [\App\Http\Controllers\InvoiceController::class, 'regenerate'])->name('regenerate');
        });

        // Payment Report Routes
        Route::prefix('payment-reports')->name('payment-reports.')->group(function () {
            Route::get('/', [\App\Http\Controllers\PaymentReportController::class, 'index'])->name('index');
            Route::get('/download-pdf', [\App\Http\Controllers\PaymentReportController::class, 'downloadPdf'])->name('download-pdf');
            Route::get('/user-history', [\App\Http\Controllers\PaymentReportController::class, 'userHistory'])->name('user-history');
        });

        // ACH Verification Routes
        Route::prefix('ach-verification')->name('ach-verification.')->group(function () {
            Route::get('/{paymentMethod}', [\App\Http\Controllers\AchVerificationController::class, 'show'])->name('show');
            Route::post('/{paymentMethod}', [\App\Http\Controllers\AchVerificationController::class, 'verify'])->name('verify');
            Route::get('/{paymentMethod}/status', [\App\Http\Controllers\AchVerificationController::class, 'status'])->name('status');
        });

        Route::get('/admin/question-manager', [QuestionManagerController::class, 'index'])->name('admin.question-manager');
        Route::get('/admin/services/{service}/questions', [QuestionManagerController::class, 'getQuestions']);
        Route::post('/admin/questions', [QuestionManagerController::class, 'createQuestion']);
        Route::get('/admin/edit-question/{question}', [QuestionManagerController::class, 'editQuestion'])->name('edit-question');
        Route::put('/admin/questions/{question}', [QuestionManagerController::class, 'updateQuestion'])->name('admin.questions.update');
        Route::delete('/admin/questions/{question}', [QuestionManagerController::class, 'deleteQuestion']);
        Route::post('/admin/questions/{question}/options', [QuestionManagerController::class, 'createOption']);
        Route::delete('/admin/options/{option}', [QuestionManagerController::class, 'deleteOption']);
        Route::post('/admin/questions/update-order', [QuestionManagerController::class, 'updateOrder']);
        Route::post('/admin/questions/{question}/update-option-order', [QuestionManagerController::class, 'updateOptionOrder']);

        Route::resource('patients.medical-records', MedicalRecordController::class);
    });

    Route::prefix('medications')->name('medications.')->group(function () {
        Route::get('generics-a-z', [MedicationsController::class, 'genericsAZ'])->name('generics-a-z');
    });

    Route::get('/doctors/signup', [DoctorsSignupController::class, 'create'])->name('doctors.register');
    Route::post('/doctors', [DoctorsSignupController::class, 'store'])->name('doctors.store');

    Route::get('/mailable', function () {
        $invoice = App\Models\Quote::find(1);

        return new App\Mail\QuoteRequested($invoice);
    });

    Route::get('/test-mailgun', function () {
        $quote = App\Models\Quote::first() ?? new App\Models\Quote([
            'company_name' => 'Test Company',
            'contact_name' => 'Test Contact',
            'email' => '<EMAIL>',
            'phone' => '************',
            'num_employees' => 10,
            'industry' => 'Technology',
            'current_insurance' => 'Yes',
            'happy_with_coverage' => 'No',
            'coverage_issues' => 'Too expensive'
        ]);

        \Illuminate\Support\Facades\Mail::to(config('mail.admin_email'))->send(new App\Mail\QuoteRequested($quote));

        return 'Email sent successfully! Check your Mailgun dashboard.';
    });

    require __DIR__.'/auth.php';
    require __DIR__.'/admin.php';
    require __DIR__.'/crm.php';
    require __DIR__.'/doctor.php';
    require __DIR__.'/front.php';
    require __DIR__.'/patient.php';
    require __DIR__.'/pharmacy.php';
    require __DIR__.'/business.php';
    require __DIR__.'/agent.php';
    require __DIR__.'/loa.php';
    require __DIR__.'/employee.php';
    require __DIR__.'/json.php';
});
Route::get('/admin/questions/tree', [QuestionManagerController::class, 'showQuestionsByService']);

Route::post('/mark-shareasale-fired/{id}', [ShareASaleController::class, 'markFired'])->name('mark-shareasale-fired');

Route::view('test', 'shareasale.test');

Route::get('/link-trust-campaign-test-page', function() {
    \Debugbar::enable();
    // Store the LinkTrust ClickID in session if it exists in the request
    if (request()->has('ClickID')) {
        session()->put('LTClickID', request()->input('ClickID'));
    }

    // Log the request for debugging
    \Illuminate\Support\Facades\Log::info('LinkTrust campaign test page accessed', [
        'ClickID' => session('LTClickID'),
        'request_params' => request()->all(),
        'user_agent' => request()->userAgent(),
        'ip' => request()->ip()
    ]);

    return view('linktrust.test');
});

Route::get('/link-trust-campaign-test', function() {
    \Debugbar::enable();
    // Store the LinkTrust ClickID in session if it exists in the request
    if (request()->has('ClickID')) {
        session()->put('LTClickID', request()->input('ClickID'));
    }

    // Log the request for debugging
    \Illuminate\Support\Facades\Log::info('LinkTrust campaign test accessed', [
        'ClickID' => session('LTClickID'),
        'request_params' => request()->all(),
        'user_agent' => request()->userAgent(),
        'ip' => request()->ip()
    ]);

    // Sleep for 2 seconds to simulate processing time
    sleep(2);

    // Redirect to the success page
    return redirect('/link-trust-campaign-test-success');
});

Route::get('/link-trust-campaign-test-success', function(\Illuminate\Http\Request $request) {
    \Debugbar::enable();
    // Simulate transaction data
    $amount = $request->query('amount', 25.00); // Default to $25 if not provided
    $transactionId = $request->query('transaction_id', 'TEST-' . uniqid());

    // If user is not logged in, create a mock user ID
    $userId = $request->user() ? $request->user()->id : 'TEST-USER-' . rand(1000, 9999);

    // Store transaction ID in session to simulate a real transaction
    session()->put('transid', $transactionId);

    // Use the LinkTrust service to track the conversion
    $linkTrustService = app(\App\Services\LinkTrustService::class);
    $result = $linkTrustService->trackPurchase($amount, $userId, $transactionId);

    return view('linktrust.success', [
        'amount' => $amount,
        'transactionId' => $transactionId,
        'clickId' => session()->get('LTClickID') ?? \Illuminate\Support\Facades\Cookie::get('LTClickID'),
        'userId' => $userId,
        'trackingUrl' => $result['tracking_url'] ?? ''
    ]);
});

// LinkTrust Example Routes
Route::prefix('examples/linktrust')->group(function() {
    Route::get('/', function() {
        return redirect()->route('examples.linktrust.trait');
    });
    Route::get('/trait', [\App\Http\Controllers\Examples\LinkTrustExampleController::class, 'exampleUsingTrait'])->name('examples.linktrust.trait');
    Route::get('/service', [\App\Http\Controllers\Examples\LinkTrustExampleController::class, 'exampleUsingService'])->name('examples.linktrust.service');
});
